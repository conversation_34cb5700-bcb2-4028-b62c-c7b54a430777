import os
import time
import base64
import sys
from pathlib import Path
from typing import Optional, List

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel
import uvicorn

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# --------------------------
# FastAPI Setup
# --------------------------
app = FastAPI(title="Tele Taleem PowerBill API", version="2.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --------------------------
# Models
# --------------------------
class BillRequest(BaseModel):
    refNumber: str

class BillResponse(BaseModel):
    success: bool
    message: str
    file_path: Optional[str] = None

class FileResponse(BaseModel):
    refNumber: str
    status: str

# --------------------------
# Paths
# --------------------------
BASE_DIR = Path(__file__).resolve().parent
ref_file_path = BASE_DIR / "ref_numbers.txt"
download_dir = Path.home() / "Desktop" / "bills"
download_dir.mkdir(parents=True, exist_ok=True)

# --------------------------
# Chrome Driver
# --------------------------
def get_chrome_driver(headless=True):
    options = Options()
    if headless:
        options.add_argument("--headless=new")
    options.add_argument("--disable-gpu")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-popup-blocking")
    options.add_argument("--disable-notifications")
    options.add_argument("--disable-infobars")
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    return driver

# --------------------------
# Save PDF
# --------------------------
def save_pdf(driver, save_path):
    pdf = driver.execute_cdp_cmd("Page.printToPDF", {"printBackground": True, "format": "A4"})
    with open(save_path, "wb") as f:
        f.write(base64.b64decode(pdf["data"]))
    print(f"✅ Saved: {save_path}")

# --------------------------
# Close extra tabs
# --------------------------
def close_extra_tabs(driver, main_handle):
    for handle in driver.window_handles:
        if handle != main_handle:
            try:
                driver.switch_to.window(handle)
                driver.close()
            except:
                pass
    driver.switch_to.window(main_handle)

# --------------------------
# Download single bill
# --------------------------
def download_single_bill(driver, ref_number: str):
    driver.get("https://iescobill.pk/")
    main_window = driver.current_window_handle

    # Enter reference number
    ref_input = WebDriverWait(driver, 15).until(EC.presence_of_element_located((By.ID, "reference")))
    ref_input.clear()
    ref_input.send_keys(ref_number)

    driver.find_element(By.ID, "checkBill").click()
    time.sleep(2)

    close_extra_tabs(driver, main_window)

    # Click on view/open bill
    open_btn = WebDriverWait(driver, 20).until(
        EC.element_to_be_clickable((By.CSS_SELECTOR, "form#billForm button[type='submit']"))
    )
    open_btn.click()

    # Wait for new tab
    WebDriverWait(driver, 15).until(lambda d: len(d.window_handles) > 1)
    driver.switch_to.window(driver.window_handles[-1])

    print(f"🌐 Bill page opened: {driver.current_url}")

    # Wait for page to load
    try:
        WebDriverWait(driver, 20).until(EC.invisibility_of_element_located((By.ID, "loader-container")))
    except:
        pass

    WebDriverWait(driver, 15).until(
        EC.element_to_be_clickable((By.CSS_SELECTOR, "button[onclick='window.print()']"))
    )
    time.sleep(1)

    save_path = download_dir / f"{ref_number}.pdf"
    save_pdf(driver, str(save_path))

# --------------------------
# API Endpoints
# --------------------------
@app.get("/api/health")
async def health_check():
    return {"status": "OK", "message": "Tele Taleem PowerBill API is running 🚀"}

@app.post("/api/run-single", response_model=BillResponse)
async def run_single(request: BillRequest):
    ref_number = request.refNumber.strip()
    if not ref_number:
        raise HTTPException(status_code=400, detail="Reference number required")
    driver = get_chrome_driver(headless=True)
    try:
        download_single_bill(driver, ref_number)
        file_path = download_dir / f"{ref_number}.pdf"
        return BillResponse(success=True, message="Bill downloaded", file_path=str(file_path))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed: {e}")
    finally:
        driver.quit()

@app.get("/api/run-file")
async def run_file_bills():
    if not ref_file_path.exists():
        return JSONResponse(status_code=404, content={"error": f"File not found: {ref_file_path}"})
    with open(ref_file_path, "r") as f:
        ref_numbers = [line.strip() for line in f if line.strip()]
    if not ref_numbers:
        return JSONResponse(status_code=400, content={"error": "No reference numbers found"})
    
    driver = get_chrome_driver(headless=True)
    results: List[FileResponse] = []
    try:
        for ref in ref_numbers:
            try:
                download_single_bill(driver, ref)
                results.append({"refNumber": ref, "status": "✅ Completed"})
            except Exception as e:
                results.append({"refNumber": ref, "status": f"❌ Failed: {e}"})
        return {"results": results}
    finally:
        driver.quit()

# --------------------------
# CLI Mode
# --------------------------
def run_all_bills_from_file():
    driver = get_chrome_driver(headless=False)
    try:
        with open(ref_file_path, "r") as f:
            ref_numbers = [line.strip() for line in f if line.strip()]
        for ref in ref_numbers:
            print(f"⚡ Downloading bill: {ref}")
            download_single_bill(driver, ref)
        print("🎉 All downloads finished.")
    finally:
        driver.quit()

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "--server":
            uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
        elif sys.argv[1] == "--file":
            run_all_bills_from_file()
        else:
            ref_number = sys.argv[1]
            driver = get_chrome_driver(headless=False)
            download_single_bill(driver, ref_number)
    else:
        print("Usage:")
        print("  python app.py --server   # Run API server")
        print("  python app.py <ref_no>   # Run for single ref")
        print("  python app.py --file     # Run all from file")
