import React from "react";
import { useLocation, <PERSON> } from "react-router-dom";
import "./App.css";

function BillList() {
  const location = useLocation();
  const references = location.state?.references || [];

  return (
    <div className="bill-list">
      <h2 className="main-title" style={{ color: "#360f5a" }}>All Reference Numbers</h2>
      {references.length === 0 ? (
        <p className="sub-title">⚠️ No reference numbers found.</p>
      ) : (
        <table className="bill-table">
          <thead>
            <tr>
              <th>#</th>
              <th>Reference Number</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {references.map((ref, index) => (
              <tr key={index}>
                <td>{index + 1}</td>
                <td>{ref}</td>
                <td>
                  <a
                    href={`https://bill.pitc.com.pk/gbill.aspx?refno=${ref}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="view-btn"
                  >
                    View Bill
                  </a>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}

      <Link to="/" className="back-btn">
        ⬅ Back
      </Link>
    </div>
  );
}

export default BillList;
