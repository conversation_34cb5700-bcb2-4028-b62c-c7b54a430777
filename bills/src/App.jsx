import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import DownloadBill from "./DownloadBill";
import BillList from "./BillList";
import logo from "./teletaleem.jpeg"; // your logo
import "./App.css";

function Home() {
  return (
    <div className="app-container">
      {/* Logo */}
      <img src={logo} alt="Telli Taleem" className="logo" />

      {/* Main Title */}
      <h1 className="main-title" style={{ color: "#360f5a" }}>PowerBill</h1>

      {/* Bill Download Component */}
      <DownloadBill />
    </div>
  );
}

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/bills" element={<BillList />} />
      </Routes>
    </Router>
  );
}

export default App;
