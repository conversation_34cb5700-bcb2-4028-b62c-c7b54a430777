import React, { useState } from "react";
import "./App.css";

export default function DownloadBill() {
  const [refNumber, setRefNumber] = useState("");
  const [status, setStatus] = useState("");
  const [statusList, setStatusList] = useState([]);
  const [loadingFile, setLoadingFile] = useState(false);

  // Single bill download
  const downloadBill = async () => {
    if (!refNumber) {
      setStatus("Enter a reference number!");
      return;
    }
    setStatus("Downloading...");
    try {
      const res = await fetch("http://127.0.0.1:8000/api/run-python", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ refNumber }),
      });
      const data = await res.json();
      if (res.ok) setStatus(`✅ ${data.message}`);
      else setStatus(`❌ ${data.detail}`);
    } catch (err) {
      console.error(err);
      setStatus("❌ Error running script");
    }
  };

  // Download all bills from file
  const downloadAllBills = async () => {
    setLoadingFile(true);
    setStatusList([]);
    try {
      const res = await fetch("http://127.0.0.1:8000/api/run-file");
      const data = await res.json();
      if (res.ok) setStatusList(data.results || []);
      else setStatusList([{ refNumber: "-", status: data.error }]);
    } catch (err) {
      console.error(err);
      setStatusList([{ refNumber: "-", status: "❌ Error running script" }]);
    } finally {
      setLoadingFile(false);
    }
  };

  return (
    <div className="container">
      <h2 style={{ color: "#360f5a" }}>Download Single Bill</h2>
      <div className="input-group">
        <input
          type="text"
          placeholder="Enter Reference Number"
          value={refNumber}
          onChange={(e) => setRefNumber(e.target.value)}
        />
        <button onClick={downloadBill}>Download Bill</button>
      </div>
      <p className="status">{status}</p>

      <hr />

      <h2 style={{ color: "#360f5a" }}>Download Bills from File</h2>
      <button onClick={downloadAllBills} disabled={loadingFile}>
        {loadingFile ? "Downloading..." : "Download All Bills from File"}
      </button>
      <ul>
        {statusList.map((item, idx) => (
          <li key={idx}>
            {item.refNumber}: {item.status}
          </li>
        ))}
      </ul>
    </div>
  );
}
